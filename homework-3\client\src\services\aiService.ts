// AI服务接口

export type StreamMindMapData =
  | { type: "node"; node: any }
  | { type: "content"; content: string; fullContent: string; nodes: any[] }
  | { type: "done" }
  | { type: string;[key: string]: any }

export async function generateMindMapStream(
  prompt: string,
  onData: (data: StreamMindMapData) => void,
  onDone?: () => void,
  onError?: (err: any) => void
) {
  try {
    const url = `http://localhost:3001/api/stream-mindmap?prompt=${encodeURIComponent(prompt)}`
    console.log(`📡 请求URL: ${url}`)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache"
      }
    })

    if (!response.body) throw new Error("无流式响应")

    const reader = response.body.getReader()
    const decoder = new TextDecoder("utf-8")
    let buffer = "" // 缓存未解析的流数据
    let rest = "" // 记录每次未完全处理的行数据

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      // 解码获取的流数据
      buffer += decoder.decode(value, { stream: true })

      // 按行处理数据
      const lines = buffer.split("\n")
      buffer = "" // 清空buffer，开始处理下一部分数据

      for (const line of lines) {

        if (line.trim() === "") continue // 跳过空行

        if (line.startsWith("data: ")) {
          try {
            const jsonStr = line.slice(6)
            const data = JSON.parse(jsonStr) // 提取 JSON 数据
            // 立即调用回调
            onData(data)

            if (data.type === "done") {
              console.log("收到done信号，数据传输完成")
              if (onDone) {
                onDone()
              }
            }
          } catch (parseError) {
            console.log("JSON解析失败:", line.slice(6), parseError)
          }
        } else if (line.startsWith("event: end")) {
          console.log("收到结束事件")
          if (onDone) onDone()
        } else if (line.startsWith("event: error")) {
          console.log("收到错误事件:", line)
          if (onError) onError(line)
        }
      }

      // 保存剩余未处理的部分
      if (lines[lines.length - 1] && !lines[lines.length - 1].endsWith("\n")) {
        rest = lines[lines.length - 1]
        buffer = rest // 将未处理的部分放入buffer
      }
    }

    if (onDone) onDone() // 确保在流结束后触发 onDone
  } catch (err) {
    console.error("流式请求错误:", err)
    if (onError) onError(err) // 传递错误
  }
}
