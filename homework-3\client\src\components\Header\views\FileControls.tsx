import {
  ChevronDownIcon,
  HamburgerMenuIcon,
  HomeIcon,
  PlusIcon,
  TrashIcon
} from "@radix-ui/react-icons"
import React, { useCallback } from "react"
import { resetMindMap } from "../../../store"
import { useAppDispatch } from "../../../store/hooks"
import { TooltipButton } from "../../common/TooltipButton"
import { TooltipDropdownButton } from "../../common/TooltipDropMenus"
import { TooltipPopoverButton } from "../../common/TooltipPopover"
import { NewFiles } from "../config/constants"
import type { FileControlsProps } from "../type/types"
import { exportMindMap } from "../../../utils"

export const FileControls: React.FC<FileControlsProps> = () => {
  const dispatch = useAppDispatch()
  const handleReset = useCallback(() => {
    if (confirm("确定要重置思维导图吗？这将清除所有数据并无法恢复。")) {
      dispatch(resetMindMap())
      console.log("思维导图已重置")
    }
  }, [dispatch])
  return (
    <div className="file-controls">
      <TooltipButton label="主页" className="header-file">
        <HomeIcon />
      </TooltipButton>
      <TooltipButton label="重置" onClick={handleReset}>
        <TrashIcon />
      </TooltipButton>
      <TooltipDropdownButton
        label="新建"
        icon={<PlusIcon />}
        menuItems={NewFiles}
      />
      <TooltipPopoverButton
        icon={<HamburgerMenuIcon />}
        tooltipText="文件操作"
        popoverContent={<div>Popover 面板内容</div>}
      />
      <TooltipButton label="导出" className="header-file">
        <HomeIcon onClick={exportMindMap} />
      </TooltipButton>
      <TooltipPopoverButton
        icon={<ChevronDownIcon />}
        tooltipText="更多选项"
        popoverContent={
          <div className="card-panel">
            {/* 顶部卡片 - 文件信息和操作 */}
            <div className="card-top">
              <div className="card-top-file">
                <div className="file-info">
                  <div className="file-name">测试文件</div>
                  <div className="file-actions"></div>
                </div>
              </div>
            </div>

            {/* 底部卡片 - 位置信息和操作 */}
            <div className="card-bottom">
              <div className="location-info">
                <div className="folder-icon">📁</div>
                <div className="location-path">test</div>
              </div>
              <div className="location-actions"></div>
            </div>
          </div>
        }
      />
    </div>
  )
}
